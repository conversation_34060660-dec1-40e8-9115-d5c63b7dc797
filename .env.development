# MongoDB Configuration
MONGODB_URI=***************************************************
DB_NAME=sarafi
DB_URI=mongodb://127.0.0.1:27017
DB_USERNAME=admin
DB_PASSWORD=T1ChdjFL7e7x
# Application Configuration
PORT=3000

# JWT Configuration
JWT_SECRET=87d7325933f6073746073df4225e2832eaccf352147cd0dda2c62c8ffd419eb89037d7bced444ce827509a66de3a2fd3b88145ccd5e4133ae6000a8442d75c862e769469d6ed5c0e2d009090a933ed5355b910abae127f163736c1216dc32e9e792eb0c1ef7b5ccd825c83979c162f9decf5a45bd85830c912eb3a91047ba122dfa6e57e42a8cbba85d58b500f028a9b53c24bd7908b03b74d52705c400502d69f49ba19654f43ac807a87a80bd67b813a0bee3489efb24b3ea0baec3ffa5e81548e5502003f2482e2850edb59395410bef5bfe5af883fbdbdee6c659cf0f321508d529ca15de16942fb9211d7ff09d62aa1a69a95b54b455e15f4b917871991
JWT_EXPIRES_IN=100d
NODE_ENV=development
IPPNALE_TOKEN=OWYxZjYwZmQtNGQ4OS00MzMyLTg3ZjktOWM4NmQ0MTE1NGNmZDQxYTM5NTBjOWE4NzEzNDVjM2IzN2EwZmQ3MzE1ZTc=

# Nobitex API Configuration
NOBITEX_API_URL=https://api.nobitex.ir/market/stats
RESEND_API_KEY=re_3BGJkWQS_3PT7D8qwVC17f3fVGXt2MXMi
FRONTEND_URL=http://localhost:4000
ZIBAL_API_KEY=339614fa04e04914b4437702c7ada9ef