# Zibal Card Verification Integration

## Overview

The application now integrates with Zibal's Card Verification API to verify that a bank card number and cardholder name match according to official records. This verification is performed automatically when a user creates or updates a bank card.

## API Endpoint Used

**Zibal Card Verification API**
- URL: `https://api.zibal.ir/v1/facility/checkCardWithName`
- Method: `POST`
- Content-Type: `application/json`

### Request Format
```json
{
    "cardNumber": "63621XXX33007361",
    "name": "محمد صادقی"
}
```

### Response Format
```json
{
    "result": 1,
    "message": "موفق",
    "data": {
        "matched": true
    }
}
```

## Integration Details

### When Verification Occurs

The Zibal card verification is automatically triggered when:
1. A user creates a new bank card via the `POST /cards` endpoint
2. A user updates an existing bank card via the `PATCH /cards/:id` endpoint and changes either:
   - Card number (`cardNumber`)
   - Account holder name (`accountHolderName`)

### Verification Process

1. **Duplicate Check**: The system first verifies that the card number and IBAN are not already registered for the user
2. **Zibal API Call**: The system calls Zibal's API with the card number and account holder name
3. **Verification Result**:
   - **Success**: If `result === 1` and `data.matched === true`, the card is created/updated
   - **Failure**: If verification fails, a `BadRequestException` is thrown with an appropriate error message
   - **API Error**: If the Zibal API is unavailable, a `BadRequestException` is thrown to prevent card creation/update

### Code Implementation

The verification logic is implemented in the `BankCardsService` class:

```typescript
private async verifyCardWithName(cardNumber: string, name: string): Promise<boolean> {
  try {
    const response = await axios.post(
      'https://api.zibal.ir/v1/facility/checkCardWithName',
      {
        cardNumber: cardNumber,
        name: name
      },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000,
      }
    );

    return response.data.result === 1 && response.data.data?.matched === true;
  } catch (error) {
    // Log error and throw exception to prevent card creation/update
    throw new BadRequestException('امکان تأیید تطابق شماره کارت و نام صاحب کارت وجود ندارد. لطفاً دوباره تلاش کنید.');
  }
}
```

## Error Messages

### Persian Error Messages
- **Verification Failed**: `شماره کارت و نام صاحب کارت مطابقت ندارند. لطفاً اطلاعات خود را بررسی کنید.`
- **API Error**: `امکان تأیید تطابق شماره کارت و نام صاحب کارت وجود ندارد. لطفاً دوباره تلاش کنید.`

## Security Features

1. **Timeout**: API calls have a 10-second timeout to prevent hanging requests
2. **Error Handling**: API failures prevent card creation/update to maintain data integrity
3. **Logging**: All verification attempts are logged for audit purposes
4. **Data Privacy**: Card numbers are masked in logs (only last 4 digits shown)
5. **Comprehensive Validation**: Both creation and update operations are protected

## Usage Examples

### Creating a New Bank Card

```bash
curl -X POST http://localhost:3000/cards \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "cardNumber": "****************",
    "bankName": "Bank Mellat",
    "accountHolderName": "احمد محمدی",
    "iban": "IR****************78901234",
    "description": "کارت اصلی"
  }'
```

**Success Response** (if verification passes):
```json
{
  "success": true,
  "message": "Bank card created successfully",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "userId": "507f1f77bcf86cd799439012",
    "cardNumber": "**** **** **** 3456",
    "bankName": "Bank Mellat",
    "accountHolderName": "احمد محمدی",
    "iban": "IR****************78901234",
    "isActive": true,
    "description": "کارت اصلی",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

**Error Response** (if verification fails):
```json
{
  "success": false,
  "message": "شماره کارت و نام صاحب کارت مطابقت ندارند. لطفاً اطلاعات خود را بررسی کنید.",
  "statusCode": 400,
  "error": "Bad Request"
}
```

### Updating a Bank Card

```bash
curl -X PATCH http://localhost:3000/cards/507f1f77bcf86cd799439011 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "accountHolderName": "احمد محمدی جدید"
  }'
```

## Logs to Monitor

When testing, monitor the application logs for:

```
[BankCardsService] Verifying card **** **** **** 3456 with name "احمد محمدی" via Zibal API
[BankCardsService] Card and name verification successful for **** **** **** 3456
```

Or in case of errors:
```
[BankCardsService] Card and name verification failed for **** **** **** 3456: عدم تطابق
[BankCardsService] Error verifying card and name via Zibal API: Network error
```

## Testing

The service includes comprehensive unit tests that mock the Zibal API responses:
- Successful verification scenarios
- Failed verification scenarios
- API error scenarios
- Existing card/IBAN conflict scenarios

Run tests with:
```bash
npm run test -- bank-cards.service.spec.ts
```

## Future Enhancements

1. **Retry Logic**: Implement automatic retry for transient API failures
2. **Caching**: Cache successful verifications to avoid repeated API calls for the same card/name combination
3. **Rate Limiting**: Implement rate limiting for API calls to prevent abuse
4. **Configuration**: Make API endpoint configurable via environment variables
5. **Batch Verification**: Support batch verification for multiple cards
