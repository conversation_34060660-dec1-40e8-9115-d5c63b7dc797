import { Is<PERSON><PERSON>, IsNotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, <PERSON>, <PERSON><PERSON>, IsMongoId } from 'class-validator';

export class CreateOrderDto {
  @IsMongoId({ message: 'Card ID must be a valid MongoDB ObjectId' })
  @IsNotEmpty()
  cardId: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(10, { message: 'From currency name must not exceed 10 characters' })
  fromCurrency: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(10, { message: 'To currency name must not exceed 10 characters' })
  toCurrency: string;

  @IsNumber({}, { message: 'From amount must be a valid number' })
  @Min(0.01, { message: 'From amount must be at least 0.01' })
  fromAmount: number;

  @IsNumber({}, { message: 'To amount must be a valid number' })
  @Min(0.01, { message: 'To amount must be at least 0.01' })
  toAmount: number;

  @IsNumber({}, { message: 'Exchange rate must be a valid number' })
  // @Min(0.0001, { message: 'Exchange rate must be at least 0.0001' })
  exchangeRate: number;

  @IsOptional()
  @IsString()
  @MaxLength(100, { message: 'Deposit ID must not exceed 100 characters' })
  depositId?: string;
}
