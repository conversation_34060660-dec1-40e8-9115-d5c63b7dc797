# Bank Cards Module

This module provides CRUD operations for user bank cards with JWT authentication and user-specific access control.

## Features

- ✅ JWT Authentication required for all endpoints
- ✅ User-specific bank card management (users can only access their own cards)
- ✅ Iranian bank card and IBAN format validation
- ✅ Card number masking for security (only last 4 digits shown)
- ✅ Duplicate card number and IBAN prevention per user
- ✅ Active/inactive card status management
- ✅ Comprehensive error handling and validation

## API Endpoints

### POST /bank-cards
Creates a new bank card for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Request Body:**
```json
{
  "cardNumber": "****************",
  "bankName": "Bank Mellat",
  "accountHolderName": "احمد محمدی",
  "iban": "IR****************78901234",
  "description": "کارت اصلی"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Bank card created successfully",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "userId": "507f1f77bcf86cd799439012",
    "cardNumber": "**** **** **** 3456",
    "bankName": "Bank Mellat",
    "accountHolderName": "احمد محمدی",
    "iban": "IR****************78901234",
    "isActive": true,
    "description": "کارت اصلی",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### GET /bank-cards
Returns all bank cards for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Response:**
```json
{
  "success": true,
  "message": "Bank cards retrieved successfully",
  "data": [
    {
      "id": "507f1f77bcf86cd799439011",
      "userId": "507f1f77bcf86cd799439012",
      "cardNumber": "**** **** **** 3456",
      "bankName": "Bank Mellat",
      "accountHolderName": "احمد محمدی",
      "iban": "IR****************78901234",
      "isActive": true,
      "description": "کارت اصلی",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "count": 1
}
```

### GET /bank-cards/active-count
Returns the count of active bank cards for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Response:**
```json
{
  "success": true,
  "message": "Active bank cards count retrieved successfully",
  "data": {
    "activeCardsCount": 2
  }
}
```

### GET /bank-cards/:id
Returns a specific bank card by ID for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Response:**
```json
{
  "success": true,
  "message": "Bank card retrieved successfully",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "userId": "507f1f77bcf86cd799439012",
    "cardNumber": "**** **** **** 3456",
    "bankName": "Bank Mellat",
    "accountHolderName": "احمد محمدی",
    "iban": "IR****************78901234",
    "isActive": true,
    "description": "کارت اصلی",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### PATCH /bank-cards/:id
Updates an existing bank card for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Request Body:**
```json
{
  "bankName": "Bank Saderat",
  "isActive": false
}
```

**Response:**
```json
{
  "success": true,
  "message": "Bank card updated successfully",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "userId": "507f1f77bcf86cd799439012",
    "cardNumber": "**** **** **** 3456",
    "bankName": "Bank Saderat",
    "accountHolderName": "احمد محمدی",
    "iban": "IR****************78901234",
    "isActive": false,
    "description": "کارت اصلی",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### DELETE /bank-cards/:id
Deletes a bank card for the authenticated user.

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
```

**Response:**
```json
{
  "success": true,
  "message": "Bank card deleted successfully"
}
```

## Validation Rules

- **Card Number**: Must be exactly 16 digits
- **Bank Name**: Must be between 2 and 50 characters
- **Account Holder Name**: Must be between 2 and 100 characters
- **IBAN**: Must be in Iranian format (IR followed by 24 digits)
- **Description**: Optional, maximum 200 characters
- **Duplicate Prevention**: Card number and IBAN must be unique per user
- **Card Verification**: Card number and account holder name must match according to Zibal API verification

## Security Features

- JWT authentication required for all endpoints
- User-specific access control (users can only access their own cards)
- Card numbers are masked in responses (only last 4 digits shown)
- Duplicate card number and IBAN prevention per user
- **Zibal API Integration**: Automatic verification of card number and cardholder name match
- Card numbers are masked in logs for security (only last 4 digits shown)
- Comprehensive error handling with appropriate HTTP status codes

## Database Schema

### BankCard Schema
```typescript
{
  userId: ObjectId;           // Reference to User, required
  cardNumber: string;         // 16-digit card number, required
  bankName: string;          // Bank name, required, 2-50 chars
  accountHolderName: string; // Account holder name, required, 2-100 chars
  iban: string;              // Iranian IBAN format, required
  isActive: boolean;         // Default: true
  description?: string;      // Optional, max 200 chars
  createdAt: Date;           // Auto-generated
  updatedAt: Date;           // Auto-generated
}
```

## Zibal Card Verification Integration

The bank cards module integrates with Zibal's Card Verification API to ensure that the card number and account holder name match according to official records.

### When Verification Occurs

- **Card Creation**: When creating a new bank card via `POST /cards`
- **Card Update**: When updating card number or account holder name via `PATCH /cards/:id`

### Verification Process

1. System validates input data and checks for duplicates
2. Calls Zibal API with card number and account holder name
3. If verification succeeds (`matched: true`), the card is created/updated
4. If verification fails, returns `400 Bad Request` with Persian error message

### Error Messages

- **Verification Failed**: `شماره کارت و نام صاحب کارت مطابقت ندارند. لطفاً اطلاعات خود را بررسی کنید.`
- **API Error**: `امکان تأیید تطابق شماره کارت و نام صاحب کارت وجود ندارد. لطفاً دوباره تلاش کنید.`

### Example Error Response

```json
{
  "success": false,
  "message": "شماره کارت و نام صاحب کارت مطابقت ندارند. لطفاً اطلاعات خود را بررسی کنید.",
  "statusCode": 400,
  "error": "Bad Request"
}
```

For detailed information about the Zibal integration, see [ZIBAL_CARD_VERIFICATION.md](../../ZIBAL_CARD_VERIFICATION.md).

## Error Handling

- **400 Bad Request**: Validation errors, malformed data
- **401 Unauthorized**: Missing or invalid JWT token
- **403 Forbidden**: Attempting to access another user's bank card
- **404 Not Found**: Bank card not found
- **409 Conflict**: Duplicate card number or IBAN for the same user

## Testing

Run the tests with:
```bash
npm run test
```

The module includes comprehensive unit tests for both the service and controller.
