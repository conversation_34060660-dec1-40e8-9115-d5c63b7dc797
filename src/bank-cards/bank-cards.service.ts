import { Injectable, NotFoundException, ForbiddenException, ConflictException, BadRequestException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { BankCard, BankCardDocument } from './schemas/bank-card.schema';
import { CreateBankCardDto } from './dto/create-bank-card.dto';
import { UpdateBankCardDto } from './dto/update-bank-card.dto';
import axios from 'axios';

@Injectable()
export class BankCardsService {
  private readonly logger = new Logger(BankCardsService.name);

  constructor(
    @InjectModel(BankCard.name) private bankCardModel: Model<BankCardDocument>,
  ) {}

  /**
   * Mask card number for security (show only last 4 digits)
   */
  private maskCardNumber(cardNumber: string): string {
    return '**** **** **** ' + cardNumber.slice(-4);
  }

  /**
   * Format bank card response
   */
  private formatBankCardResponse(bankCard: BankCardDocument) {
    return {
      id: bankCard._id,
      userId: bankCard.userId,
      cardNumber: this.maskCardNumber(bankCard.cardNumber),
      bankName: bankCard.bankName,
      accountHolderName: bankCard.accountHolderName,
      iban: bankCard.iban,
      isActive: bankCard.isActive,
      description: bankCard.description,
      createdAt: (bankCard as any).createdAt,
      updatedAt: (bankCard as any).updatedAt,
    };
  }

  /**
   * Verify card number and cardholder name match using Zibal API
   */
  private async verifyCardWithName(cardNumber: string, name: string): Promise<boolean> {
    try {
      this.logger.log(`Verifying card ${this.maskCardNumber(cardNumber)} with name "${name}" via Zibal API`);

      const response = await axios.post(
        'https://api.zibal.ir/v1/facility/checkCardWithName',
        {
          cardNumber: cardNumber,
          name: name
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.ZIBAL_API_KEY}`,
          },
          timeout: 10000, // 10 seconds timeout
        }
      );

      const isMatched = response.data.result === 1 && response.data.data?.matched === true;

      if (isMatched) {
        this.logger.log(`Card and name verification successful for ${this.maskCardNumber(cardNumber)}`);
      } else {
        this.logger.warn(`Card and name verification failed for ${this.maskCardNumber(cardNumber)}: ${response.data.message || 'عدم تطابق'}`);
      }

      return isMatched;
    } catch (error) {
      this.logger.error(`Error verifying card and name via Zibal API: ${error.message}`);

      if (axios.isAxiosError(error)) {
        this.logger.error(`HTTP Status: ${error.response?.status}`);
        this.logger.error(`Response Data: ${JSON.stringify(error.response?.data)}`);
      }

      // Throw exception to prevent card creation if verification fails
      throw new BadRequestException('امکان تأیید تطابق شماره کارت و نام صاحب کارت وجود ندارد. لطفاً دوباره تلاش کنید.');
    }
  }

  /**
   * Create a new bank card for a user
   */
  async create(userId: string, createBankCardDto: CreateBankCardDto): Promise<any> {
    // Check if card number already exists for this user
    const existingCard = await this.bankCardModel.findOne({
      userId: new Types.ObjectId(userId),
      cardNumber: createBankCardDto.cardNumber,
    }).exec();

    if (existingCard) {
      throw new ConflictException('این شماره کارت قبلاً برای حساب شما ثبت شده است');
    }

    // Check if IBAN already exists for this user
    const existingIban = await this.bankCardModel.findOne({
      userId: new Types.ObjectId(userId),
      iban: createBankCardDto.iban,
    }).exec();

    if (existingIban) {
      throw new ConflictException('این شماره شبا قبلاً برای حساب شما ثبت شده است');
    }

    // Verify card number and cardholder name match using Zibal API
    const isCardVerified = await this.verifyCardWithName(
      createBankCardDto.cardNumber,
      createBankCardDto.accountHolderName
    );

    if (!isCardVerified) {
      throw new BadRequestException('شماره کارت و نام صاحب کارت مطابقت ندارند. لطفاً اطلاعات خود را بررسی کنید.');
    }

    const bankCard = new this.bankCardModel({
      ...createBankCardDto,
      userId: new Types.ObjectId(userId),
    });

    const savedCard = await bankCard.save();
    return this.formatBankCardResponse(savedCard);
  }

  /**
   * Get all bank cards for a specific user
   */
  async findAllByUser(userId: string): Promise<any[]> {
    const bankCards = await this.bankCardModel
      .find({ userId: new Types.ObjectId(userId) })
      .sort({ createdAt: -1 })
      .exec();

    return bankCards.map(card => this.formatBankCardResponse(card));
  }

  /**
   * Get a specific bank card by ID (user-specific)
   */
  async findOne(id: string, userId: string): Promise<any> {
    const bankCard = await this.bankCardModel.findById(id).exec();
    
    if (!bankCard) {
      throw new NotFoundException(`کارت بانکی با شناسه ${id} یافت نشد`);
    }

    // Check if the bank card belongs to the requesting user
    if (bankCard.userId.toString() !== userId) {
      throw new ForbiddenException('شما فقط به کارت‌های بانکی خود دسترسی دارید');
    }

    return this.formatBankCardResponse(bankCard);
  }

  /**
   * Update a bank card (user-specific)
   */
  async update(id: string, userId: string, updateBankCardDto: UpdateBankCardDto): Promise<any> {
    const bankCard = await this.bankCardModel.findById(id).exec();
    if (!bankCard) {
      throw new NotFoundException(`کارت بانکی با شناسه ${id} یافت نشد`);
    }
    if (bankCard.userId.toString() !== userId) {
      throw new ForbiddenException('شما فقط می‌توانید کارت‌های بانکی خود را ویرایش کنید');
    }

    // If updating card number, check for duplicates
    if (updateBankCardDto.cardNumber && updateBankCardDto.cardNumber !== bankCard.cardNumber) {
      const existingCard = await this.bankCardModel.findOne({
        userId: new Types.ObjectId(userId),
        cardNumber: updateBankCardDto.cardNumber,
        _id: { $ne: id },
      }).exec();
      if (existingCard) {
        throw new ConflictException('این شماره کارت قبلاً برای حساب شما ثبت شده است');
      }
    }

    // If updating IBAN, check for duplicates
    if (updateBankCardDto.iban && updateBankCardDto.iban !== bankCard.iban) {
      const existingIban = await this.bankCardModel.findOne({
        userId: new Types.ObjectId(userId),
        iban: updateBankCardDto.iban,
        _id: { $ne: id },
      }).exec();
      if (existingIban) {
        throw new ConflictException('این شماره شبا قبلاً برای حساب شما ثبت شده است');
      }
    }

    // If updating card number or account holder name, verify with Zibal API
    const cardNumberToVerify = updateBankCardDto.cardNumber || bankCard.cardNumber;
    const accountHolderNameToVerify = updateBankCardDto.accountHolderName || bankCard.accountHolderName;

    if ((updateBankCardDto.cardNumber && updateBankCardDto.cardNumber !== bankCard.cardNumber) ||
        (updateBankCardDto.accountHolderName && updateBankCardDto.accountHolderName !== bankCard.accountHolderName)) {

      const isCardVerified = await this.verifyCardWithName(
        cardNumberToVerify,
        accountHolderNameToVerify
      );

      if (!isCardVerified) {
        throw new BadRequestException('شماره کارت و نام صاحب کارت مطابقت ندارند. لطفاً اطلاعات خود را بررسی کنید.');
      }
    }

    const updatedCard = await this.bankCardModel
      .findByIdAndUpdate(id, updateBankCardDto, { new: true })
      .exec();
    return this.formatBankCardResponse(updatedCard!);
  }

  /**
   * Delete a bank card (user-specific)
   */
  async remove(id: string, userId: string): Promise<void> {
    const bankCard = await this.bankCardModel.findById(id).exec();
    
    if (!bankCard) {
      throw new NotFoundException(`کارت بانکی با شناسه ${id} یافت نشد`);
    }

    // Check if the bank card belongs to the requesting user
    if (bankCard.userId.toString() !== userId) {
      throw new ForbiddenException('شما فقط می‌توانید کارت‌های بانکی خود را حذف کنید');
    }

    await this.bankCardModel.findByIdAndDelete(id).exec();
  }

  /**
   * Get active bank cards count for a user
   */
  async getActiveCardsCount(userId: string): Promise<number> {
    return this.bankCardModel.countDocuments({
      userId: new Types.ObjectId(userId),
      isActive: true,
    }).exec();
  }
}
