import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { WallexService } from './wallex.service';
import { CurrenciesService } from './currencies.service';

describe('WallexService', () => {
  let service: WallexService;
  let currenciesService: CurrenciesService;
  let configService: ConfigService;

  const mockCurrenciesService = {
    updateUsdtPrice: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WallexService,
        {
          provide: CurrenciesService,
          useValue: mockCurrenciesService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<WallexService>(WallexService);
    currenciesService = module.get<CurrenciesService>(CurrenciesService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('updateUsdtPrice', () => {
    it('should update USDT price successfully', async () => {
      const mockPrice = { sellPrice: 84573, buyPrice: 84573 };
      jest.spyOn(service as any, 'fetchUsdtPrice').mockResolvedValue(mockPrice);
      mockCurrenciesService.updateUsdtPrice.mockResolvedValue({});

      await service.updateUsdtPrice();

      expect(mockCurrenciesService.updateUsdtPrice).toHaveBeenCalledWith(
        mockPrice.sellPrice,
        mockPrice.buyPrice,
      );
    });

    it('should handle errors gracefully', async () => {
      jest.spyOn(service as any, 'fetchUsdtPrice').mockResolvedValue(null);

      await service.updateUsdtPrice();

      expect(mockCurrenciesService.updateUsdtPrice).not.toHaveBeenCalled();
    });
  });

  describe('manualUpdateUsdtPrice', () => {
    it('should trigger manual update', async () => {
      const updateSpy = jest.spyOn(service, 'updateUsdtPrice').mockResolvedValue();

      await service.manualUpdateUsdtPrice();

      expect(updateSpy).toHaveBeenCalled();
    });
  });
}); 