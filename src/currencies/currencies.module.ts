import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { CurrenciesService } from './currencies.service';
import { CurrenciesController } from './currencies.controller';
import { PriceUpdateService } from './nobitex.service';
import { PriceUpdateServiceChand } from './chand.service';
import { WallexService } from './wallex.service';
import { Currency, CurrencySchema } from './schemas/currency.schema';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Currency.name, schema: CurrencySchema }]),
    ConfigModule,
  ],
  controllers: [CurrenciesController],
  providers: [CurrenciesService, PriceUpdateService, PriceUpdateServiceChand, WallexService],
  exports: [CurrenciesService],
})
export class CurrenciesModule {}
