import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Currency, CurrencyDocument } from './schemas/currency.schema';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';

@Injectable()
export class CurrenciesService {
  private readonly logger = new Logger(CurrenciesService.name);

  constructor(
    @InjectModel(Currency.name) private currencyModel: Model<CurrencyDocument>,
  ) {}

  async create(createCurrencyDto: CreateCurrencyDto): Promise<Currency> {
    const createdCurrency = new this.currencyModel(createCurrencyDto);
    return createdCurrency.save();
  }

  async findAll(): Promise<Currency[]> {
    return this.currencyModel.find().exec();
  }

  async findOne(id: string): Promise<Currency> {
    const currency = await this.currencyModel.findById(id).exec();
    if (!currency) {
      throw new NotFoundException(`Currency with ID ${id} not found`);
    }
    return currency;
  }

  async update(id: string, updateCurrencyDto: UpdateCurrencyDto): Promise<Currency> {
    const updatedCurrency = await this.currencyModel
      .findByIdAndUpdate(id, updateCurrencyDto, { new: true })
      .exec();
    
    if (!updatedCurrency) {
      throw new NotFoundException(`Currency with ID ${id} not found`);
    }
    
    return updatedCurrency;
  }

  async remove(id: string): Promise<void> {
    const result = await this.currencyModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException(`Currency with ID ${id} not found`);
    }
  }

  async findByName(name: string): Promise<Currency> {
    const currency = await this.currencyModel.findOne({ name }).exec();
    if (!currency) {
      throw new NotFoundException(`Currency with name ${name} not found`);
    }
    return currency;
  }

  /**
   * Update USDT price by name
   * Used by the price update service
   */
  async updateUsdtPrice(sellPrice: number, buyPrice: number): Promise<Currency> {
    this.logger.log(`Updating USDT price - Sell: ${sellPrice}, Buy: ${buyPrice}`);

    // First try to update existing USDT currency
    let updatedCurrency = await this.currencyModel
      .findOneAndUpdate(
        { $or: [{ name: 'USDT' }, { name: 'usdt' }] },
        { sellPrice, buyPrice },
        { new: true }
      )
      .exec();

    // If no USDT currency exists, create one
    if (!updatedCurrency) {
      this.logger.log('USDT currency not found, creating new one');
      const newCurrency = new this.currencyModel({
        name: 'USDT',
        fa: 'تتر',
        sellPrice,
        buyPrice,
      });
      updatedCurrency = await newCurrency.save();
    }

    this.logger.log('USDT price updated successfully');
    return updatedCurrency;
  }

  /**
   * Update or create a currency by name and fa (Persian name)
   */
  async updateOrCreateCurrency(name: string, fa: string, sellPrice: number, buyPrice: number): Promise<Currency | null> {
    this.logger.log(`Updating ${name} price - Sell: ${sellPrice}`);
    let updatedCurrency = await this.currencyModel
      .findOneAndUpdate(
        { $or: [{ name }, { name: name.toLowerCase() }] },
        { sellPrice, buyPrice },
        { new: true }
      )
      .exec();
    this.logger.log(`${name} price updated successfully`);
    return updatedCurrency;
  }
}
