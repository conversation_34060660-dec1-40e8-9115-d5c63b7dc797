import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { CurrenciesService } from './currencies.service';
import { PriceUpdateService } from './nobitex.service';
import { PriceUpdateServiceChand } from './chand.service';
import { WallexService } from './wallex.service';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';

@Controller('currencies')
export class CurrenciesController {
  constructor(
    private readonly currenciesService: CurrenciesService,
    private readonly priceUpdateService: PriceUpdateService,
    private readonly priceUpdateServiceChand: PriceUpdateServiceChand,
    private readonly wallexService: WallexService
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createCurrencyDto: CreateCurrencyDto) {
    return this.currenciesService.create(createCurrencyDto);
  }

  @Get()
  findAll() {
    return this.currenciesService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.currenciesService.findOne(id);
  }

  @Get('name/:name')
  findByName(@Param('name') name: string) {
    return this.currenciesService.findByName(name);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateCurrencyDto: UpdateCurrencyDto) {
    return this.currenciesService.update(id, updateCurrencyDto);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.currenciesService.remove(id);
  }

  @Post('update-usdt-price-nobitex')
  @HttpCode(HttpStatus.OK)
  async updateUsdtPrice() {
    await this.priceUpdateService.manualUpdateUsdtPrice();
    return {
      success: true,
      message: 'USDT price update triggered successfully',
    };
  }

  @Post('update-usdlira-price-chand')
  @HttpCode(HttpStatus.OK)
  async updateUsdtPriceChand() {
    await this.priceUpdateServiceChand.updateUsdLiraPrice();
    return {
      success: true,
      message: 'USDT price update triggered successfully',
    };
  }

  @Post('update-usdt-price-wallex')
  @HttpCode(HttpStatus.OK)
  async updateUsdtPriceWallex() {
    await this.wallexService.manualUpdateUsdtPrice();
    return {
      success: true,
      message: 'USDT price update from Wallex triggered successfully',
    };
  }
}
