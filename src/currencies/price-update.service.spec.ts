import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { PriceUpdateService } from './nobitex.service';
import { CurrenciesService } from './currencies.service';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('PriceUpdateService', () => {
  let service: PriceUpdateService;
  let currenciesService: CurrenciesService;
  let configService: ConfigService;

  const mockCurrenciesService = {
    updateUsdtPrice: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn().mockReturnValue('https://api.nobitex.ir/market/stats'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PriceUpdateService,
        {
          provide: CurrenciesService,
          useValue: mockCurrenciesService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<PriceUpdateService>(PriceUpdateService);
    currenciesService = module.get<CurrenciesService>(CurrenciesService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('updateUsdtPrice', () => {
    it('should successfully update USDT price', async () => {
      const mockApiResponse = {
        data: {
          status: 'ok',
          stats: {
            'usdt-irt': {
              isClosed: false,
              bestSell: '833000',
              bestBuy: '832990',
              latest: '832990',
              volumeSrc: '338565.51520987498',
              volumeDst: '278581547523.7247338831',
              mark: '832990',
              dayLow: '816000',
              dayHigh: '833000',
              dayOpen: '816250',
              dayClose: '832990',
              dayChange: '2.05',
            },
          },
        },
      };

      mockedAxios.post.mockResolvedValue(mockApiResponse);
      mockCurrenciesService.updateUsdtPrice.mockResolvedValue({
        name: 'USDT',
        sellPrice: 83300,
        buyPrice: 83299,
      });

      await service.updateUsdtPrice();

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.nobitex.ir/market/stats',
        expect.any(URLSearchParams),
        expect.objectContaining({
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          timeout: 10000,
        })
      );

      // Check that prices were converted from Rial to Toman (divided by 10)
      expect(mockCurrenciesService.updateUsdtPrice).toHaveBeenCalledWith(
        83300, // 833000 / 10
        83299  // 832990 / 10
      );
    });

    it('should handle API errors gracefully', async () => {
      mockedAxios.post.mockRejectedValue(new Error('Network error'));

      // Should not throw an error, but log it
      await expect(service.updateUsdtPrice()).resolves.not.toThrow();

      expect(mockCurrenciesService.updateUsdtPrice).not.toHaveBeenCalled();
    });

    it('should handle invalid API response', async () => {
      const mockApiResponse = {
        data: {
          status: 'error',
          stats: {},
        },
      };

      mockedAxios.post.mockResolvedValue(mockApiResponse);

      await expect(service.updateUsdtPrice()).resolves.not.toThrow();

      expect(mockCurrenciesService.updateUsdtPrice).not.toHaveBeenCalled();
    });

    it('should use latest price when bestSell/bestBuy are not available', async () => {
      const mockApiResponse = {
        data: {
          status: 'ok',
          stats: {
            'usdt-irt': {
              isClosed: false,
              bestSell: '',
              bestBuy: '',
              latest: '830000',
              volumeSrc: '338565.51520987498',
              volumeDst: '278581547523.7247338831',
              mark: '830000',
              dayLow: '816000',
              dayHigh: '833000',
              dayOpen: '816250',
              dayClose: '830000',
              dayChange: '1.68',
            },
          },
        },
      };

      mockedAxios.post.mockResolvedValue(mockApiResponse);
      mockCurrenciesService.updateUsdtPrice.mockResolvedValue({
        name: 'USDT',
        sellPrice: 83000,
        buyPrice: 83000,
      });

      await service.updateUsdtPrice();

      // Should use latest price for both sell and buy when bestSell/bestBuy are empty
      expect(mockCurrenciesService.updateUsdtPrice).toHaveBeenCalledWith(
        83000, // 830000 / 10
        83000  // 830000 / 10
      );
    });
  });

  describe('manualUpdateUsdtPrice', () => {
    it('should call updateUsdtPrice method', async () => {
      const updateSpy = jest.spyOn(service, 'updateUsdtPrice').mockResolvedValue();

      await service.manualUpdateUsdtPrice();

      expect(updateSpy).toHaveBeenCalled();
    });
  });
});
