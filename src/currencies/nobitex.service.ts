import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { CurrenciesService } from './currencies.service';

interface NobitexApiResponse {
  status: string;
  stats: {
    'usdt-irt': {
      isClosed: boolean;
      bestSell: string;
      bestBuy: string;
      volumeSrc: string;
      volumeDst: string;
      latest: string;
      mark: string;
      dayLow: string;
      dayHigh: string;
      dayOpen: string;
      dayClose: string;
      dayChange: string;
    };
  };
}

@Injectable()
export class PriceUpdateService {
  private readonly logger = new Logger(PriceUpdateService.name);
  private readonly nobitexApiUrl: string;

  constructor(
    private readonly currenciesService: CurrenciesService,
    private readonly configService: ConfigService,
  ) {
    this.nobitexApiUrl = this.configService.get<string>('NOBITEX_API_URL') || 'https://api.nobitex.ir/market/stats';
  }

  /**
   * Scheduled task to update USDT price every 10 minutes
   */
  // @Cron('*/10 * * * *', {
  //   name: 'updateUsdtPrice',
  //   timeZone: 'Asia/Tehran',
  // })
  async updateUsdtPrice(): Promise<void> {
    this.logger.log('Starting USDT price update...');

    try {
      const price = await this.fetchUsdtPrice();
      
      if (price) {
        // Convert Rial to Toman by removing the last zero
        const sellPriceToman = Math.floor(price.sellPrice / 10);
        const buyPriceToman = Math.floor(price.buyPrice / 10);
        
        await this.currenciesService.updateUsdtPrice(sellPriceToman, buyPriceToman);
        
        this.logger.log(`USDT price updated successfully - Sell: ${sellPriceToman} Toman, Buy: ${buyPriceToman} Toman`);
      }
    } catch (error) {
      this.logger.error('Failed to update USDT price:', error.message);
    }
  }

  /**
   * Fetch USDT price from Nobitex API
   */
  private async fetchUsdtPrice(): Promise<{ sellPrice: number; buyPrice: number } | null> {
    try {
      this.logger.log('Fetching USDT price from Nobitex API...');

      const formData = new URLSearchParams();
      formData.append('srcCurrency', 'usdt');
      formData.append('dstCurrency', 'irt');

      const response = await axios.post<NobitexApiResponse>(
        this.nobitexApiUrl,
        formData,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          timeout: 10000, // 10 seconds timeout
        }
      );

      if (response.data.status !== 'ok') {
        throw new Error(`Nobitex API returned status: ${response.data.status}`);
      }

      const usdtStats = response.data.stats['usdt-irt'];
      
      if (!usdtStats) {
        throw new Error('USDT-IRT stats not found in API response');
      }

      const sellPrice = parseFloat(usdtStats.bestSell);
      const buyPrice = parseFloat(usdtStats.bestBuy);
      const latestPrice = parseFloat(usdtStats.latest);

      this.logger.log(`Fetched USDT prices - Sell: ${sellPrice} Rial, Buy: ${buyPrice} Rial, Latest: ${latestPrice} Rial`);

      // Use bestSell and bestBuy if available, otherwise use latest price
      return {
        sellPrice: sellPrice || latestPrice,
        buyPrice: buyPrice || latestPrice,
      };

    } catch (error) {
      this.logger.error('Error fetching USDT price from Nobitex:', error.message);
      
      if (axios.isAxiosError(error)) {
        this.logger.error(`HTTP Status: ${error.response?.status}`);
        this.logger.error(`Response Data: ${JSON.stringify(error.response?.data)}`);
      }
      
      return null;
    }
  }

  /**
   * Manual trigger for price update (for testing purposes)
   */
  async manualUpdateUsdtPrice(): Promise<void> {
    this.logger.log('Manual USDT price update triggered');
    await this.updateUsdtPrice();
  }
}
