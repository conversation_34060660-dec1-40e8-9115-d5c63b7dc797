import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { CurrenciesService } from './currencies.service';


@Injectable()
export class PriceUpdateServiceChand {
  private readonly logger = new Logger(PriceUpdateServiceChand.name);

  constructor(
    private readonly currenciesService: CurrenciesService,
  ) {
  }

  /**
   * Scheduled task to update USD and Lira prices every 10 minutes
   */
  @Cron('*/10 * * * *', {
    name: 'updateUsdLiraPrice',
    timeZone: 'Asia/Tehran',
  })
  async updateUsdLiraPrice(): Promise<void> {
    this.logger.log('Starting USD and Lira price update...');
    try {
      const prices = await this.fetchUsdLiraPrices();
      if (prices) {
        if (prices.usdSell && prices.usdBuy) {
          await this.currenciesService.updateOrCreateCurrency('USD', 'دلار آمریکا', prices.usdSell, prices.usdBuy);
          this.logger.log(`USD price updated successfully - Sell: ${prices.usdSell} Toman, Buy: ${prices.usdBuy} Toman`);
        }
        if (prices.liraSell && prices.liraBuy) {
          await this.currenciesService.updateOrCreateCurrency('LIRA', 'لیر ترکیه', prices.liraSell, prices.liraBuy);
          this.logger.log(`Lira price updated successfully - Sell: ${prices.liraSell} Toman, Buy: ${prices.liraBuy} Toman`);
        }
      }
    } catch (error) {
      this.logger.error('Failed to update USD/Lira price:', error.message);
    }
  }

  /**
   * Scheduled task to update USDT price every 10 minutes
   */
  // @Cron('*/10 * * * *', {
  //   name: 'updateUsdtPrice',
  //   timeZone: 'Asia/Tehran',
  // })
  async updateUsdtPrice(): Promise<void> {
    this.logger.log('Starting USDT price update...');
    try {
      const usdtPrice = await this.fetchUsdtPrice();
      if (usdtPrice) {
        await this.currenciesService.updateOrCreateCurrency('USDT', 'تتر', usdtPrice, usdtPrice);
        this.logger.log(`USDT price updated successfully - Price: ${usdtPrice} Toman`);
      }
    } catch (error) {
      this.logger.error('Failed to update USDT price:', error.message);
    }
  }

  /**
   * Fetch USDT price from Alanchand crypto API
   */
  private async fetchUsdtPrice(): Promise<number | null> {
    try {
      this.logger.log('Fetching USDT price from Alanchand crypto API...');
      const response = await axios.get(
        'https://alanchand.com/crypto-price',
        { 
          headers: { 
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }, 
          timeout: 10000 
        }
      );
      
      const html = response.data;
      
      // Extract the JavaScript object containing crypto data
      const scriptMatch = html.match(/window\.__NUXT__\s*=\s*\(function\([^)]*\)\s*\{[^}]*return\s*\{[^}]*data:\s*\[([^\]]+)\]/);
      
      if (!scriptMatch) {
        throw new Error('Could not find crypto data in HTML');
      }
      
      // Parse the crypto data array
      // const cryptoDataMatch = scriptMatch[1].match(/crypto:\s*\[([^\]]+)\]/);
      
      // if (!cryptoDataMatch) {
      //   throw new Error('Could not find crypto array in data');
      // }
      
      // Extract USDT data
      const usdtMatch = scriptMatch[1].match(/slug:\s*"usdt"[^}]*toman:\s*(\d+)/);
      
      if (!usdtMatch) {
        throw new Error('Could not find USDT price in crypto data');
      }
      
      const usdtPrice = parseInt(usdtMatch[1], 10);
      
      if (isNaN(usdtPrice) || usdtPrice <= 0) {
        throw new Error('Invalid USDT price value');
      }
      
      return usdtPrice;
    } catch (error) {
      this.logger.error('Error fetching USDT price from Alanchand crypto API:', error.message);
      if (axios.isAxiosError(error)) {
        this.logger.error(`HTTP Status: ${error.response?.status}`);
        this.logger.error(`Response Data: ${JSON.stringify(error.response?.data)}`);
      }
      return null;
    }
  }

  /**
   * Fetch USD and Lira prices from Alanchand API
   */
  private async fetchUsdLiraPrices(): Promise<{ usdSell?: number; usdBuy?: number; liraSell?: number; liraBuy?: number } | null> {
    try {
      this.logger.log('Fetching USD and Lira prices from Alanchand API...');
      const response = await axios.post(
        'https://admin.alanchand.com/api/home',
        { lang: 'fa' },
        { headers: { 'Content-Type': 'application/json' }, timeout: 10000 }
      );
      const arz = response.data?.arz;
      if (!Array.isArray(arz)) throw new Error('Invalid response structure: arz not found');
      let usdSell, usdBuy, liraSell, liraBuy;
      for (const item of arz) {
        if (item.slug === 'usd' && Array.isArray(item.price)) {
          if (item.price[0]?.price) usdSell = item.price[0].price;
          if (item.price[2]?.low) usdBuy = item.price[2].low;
        }
        if ((item.slug === 'lira' || item.slug === 'try') && Array.isArray(item.price)) {
          if (item.price[0]?.price) liraSell = item.price[0].price;
          if (item.price[2]?.low) liraBuy = item.price[2].low;
        }
      }
      return { usdSell, usdBuy, liraSell, liraBuy };
    } catch (error) {
      this.logger.error('Error fetching USD/Lira prices from Alanchand:', error.message);
      if (axios.isAxiosError(error)) {
        this.logger.error(`HTTP Status: ${error.response?.status}`);
        this.logger.error(`Response Data: ${JSON.stringify(error.response?.data)}`);
      }
      return null;
    }
  }

  

}
