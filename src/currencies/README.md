# Currencies Module

This module manages currencies as products stored in a MongoDB collection.

## Schema

The Currency schema includes the following fields:

- `name` (string, required, unique): The name of the currency
- `fa` (string, required, unique): The Persian name of the currency
- `sellPrice` (number, required): The selling price of the currency
- `buyPrice` (number, required): The buying price of the currency
- `createdAt` (Date, auto-generated): Creation timestamp
- `updatedAt` (Date, auto-generated): Last update timestamp

## API Endpoints

### GET /currencies
Returns all currencies.

**Response:**
```json
[
  {
    "_id": "507f1f77bcf86cd799439011",
    "name": "USD",
    "sellPrice": 100,
    "buyPrice": 95,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
]
```

### GET /currencies/:id
Returns a specific currency by ID.

### GET /currencies/name/:name
Returns a specific currency by name.

### POST /currencies
Creates a new currency.

**Request Body:**
```json
{
  "name": "USD",
  "fa": "دلار آمریکا",
  "sellPrice": 100,
  "buyPrice": 95
}
```

### PATCH /currencies/:id
Updates an existing currency.

**Request Body:**
```json
{
  "sellPrice": 105
}
```

### DELETE /currencies/:id
Deletes a currency by ID.

## Validation

- All fields are validated using class-validator
- `name` must be a non-empty string and unique
- `fa` must be a non-empty string and unique
- `sellPrice` and `buyPrice` must be numbers >= 0
- Currency names must be unique

## Automatic Price Updates

The system automatically updates USDT prices every 10 minutes using the Nobitex API:
- Fetches current USDT-IRT prices from https://api.nobitex.ir/market/stats
- Converts prices from Rial to Toman (divides by 10)
- Updates or creates USDT currency record in the database
- Manual price update can be triggered via `POST /currencies/update-usdt-price`

## Testing

Run the tests with:
```bash
npm run test
```

The module includes comprehensive unit tests for both the service and controller.
