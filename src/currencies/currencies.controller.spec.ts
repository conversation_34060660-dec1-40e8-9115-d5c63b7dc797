import { Test, TestingModule } from '@nestjs/testing';
import { CurrenciesController } from './currencies.controller';
import { CurrenciesService } from './currencies.service';
import { PriceUpdateService } from './nobitex.service';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';

describe('CurrenciesController', () => {
  let controller: CurrenciesController;
  let service: CurrenciesService;
  let priceUpdateService: PriceUpdateService;

  const mockCurrency = {
    _id: '507f1f77bcf86cd799439011',
    name: 'USD',
    sellPrice: 100,
    buyPrice: 95,
  };

  const mockCurrenciesService = {
    create: jest.fn().mockResolvedValue(mockCurrency),
    findAll: jest.fn().mockResolvedValue([mockCurrency]),
    findOne: jest.fn().mockResolvedValue(mockCurrency),
    findByName: jest.fn().mockResolvedValue(mockCurrency),
    update: jest.fn().mockResolvedValue(mockCurrency),
    remove: jest.fn().mockResolvedValue(undefined),
  };

  const mockPriceUpdateService = {
    manualUpdateUsdtPrice: jest.fn().mockResolvedValue(undefined),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CurrenciesController],
      providers: [
        {
          provide: CurrenciesService,
          useValue: mockCurrenciesService,
        },
        {
          provide: PriceUpdateService,
          useValue: mockPriceUpdateService,
        },
      ],
    }).compile();

    controller = module.get<CurrenciesController>(CurrenciesController);
    service = module.get<CurrenciesService>(CurrenciesService);
    priceUpdateService = module.get<PriceUpdateService>(PriceUpdateService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new currency', async () => {
      const createCurrencyDto: CreateCurrencyDto = {
        name: 'USD',
        fa: 'دلار آمریکا',
        sellPrice: 100,
        buyPrice: 95,
      };

      const result = await controller.create(createCurrencyDto);
      expect(result).toEqual(mockCurrency);
      expect(service.create).toHaveBeenCalledWith(createCurrencyDto);
    });
  });

  describe('findAll', () => {
    it('should return an array of currencies', async () => {
      const result = await controller.findAll();
      expect(result).toEqual([mockCurrency]);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a currency by id', async () => {
      const result = await controller.findOne('507f1f77bcf86cd799439011');
      expect(result).toEqual(mockCurrency);
      expect(service.findOne).toHaveBeenCalledWith('507f1f77bcf86cd799439011');
    });
  });

  describe('findByName', () => {
    it('should return a currency by name', async () => {
      const result = await controller.findByName('USD');
      expect(result).toEqual(mockCurrency);
      expect(service.findByName).toHaveBeenCalledWith('USD');
    });
  });

  describe('update', () => {
    it('should update a currency', async () => {
      const updateCurrencyDto: UpdateCurrencyDto = {
        sellPrice: 105,
      };

      const result = await controller.update('507f1f77bcf86cd799439011', updateCurrencyDto);
      expect(result).toEqual(mockCurrency);
      expect(service.update).toHaveBeenCalledWith('507f1f77bcf86cd799439011', updateCurrencyDto);
    });
  });

  describe('remove', () => {
    it('should remove a currency', async () => {
      const result = await controller.remove('507f1f77bcf86cd799439011');
      expect(result).toBeUndefined();
      expect(service.remove).toHaveBeenCalledWith('507f1f77bcf86cd799439011');
    });
  });

  describe('updateUsdtPrice', () => {
    it('should trigger USDT price update', async () => {
      const result = await controller.updateUsdtPrice();

      expect(result).toEqual({
        success: true,
        message: 'USDT price update triggered successfully',
      });
      expect(priceUpdateService.manualUpdateUsdtPrice).toHaveBeenCalled();
    });
  });
});
