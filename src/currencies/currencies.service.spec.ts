import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { NotFoundException } from '@nestjs/common';
import { CurrenciesService } from './currencies.service';
import { Currency, CurrencyDocument } from './schemas/currency.schema';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';

describe('CurrenciesService', () => {
  let service: CurrenciesService;
  let model: Model<CurrencyDocument>;

  const mockCurrency = {
    _id: '507f1f77bcf86cd799439011',
    name: 'USD',
    sellPrice: 100,
    buyPrice: 95,
    save: jest.fn().mockResolvedValue(this),
  };

  const mockCurrencyModel = jest.fn().mockImplementation(() => ({
    save: jest.fn().mockResolvedValue(mockCurrency),
  }));

  Object.assign(mockCurrencyModel, {
    find: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
    findByIdAndDelete: jest.fn(),
    findOne: jest.fn(),
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CurrenciesService,
        {
          provide: getModelToken(Currency.name),
          useValue: mockCurrencyModel,
        },
      ],
    }).compile();

    service = module.get<CurrenciesService>(CurrenciesService);
    model = module.get<Model<CurrencyDocument>>(getModelToken(Currency.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new currency', async () => {
      const createCurrencyDto: CreateCurrencyDto = {
        name: 'USD',
        fa: 'دلار آمریکا',
        sellPrice: 100,
        buyPrice: 95,
      };

      const result = await service.create(createCurrencyDto);
      expect(result).toEqual(mockCurrency);
      expect(mockCurrencyModel).toHaveBeenCalledWith(createCurrencyDto);
    });
  });

  describe('findAll', () => {
    it('should return an array of currencies', async () => {
      const currencies = [mockCurrency];
      jest.spyOn(model, 'find').mockReturnValue({
        exec: jest.fn().mockResolvedValue(currencies),
      } as any);

      const result = await service.findAll();
      expect(result).toEqual(currencies);
    });
  });

  describe('findOne', () => {
    it('should return a currency by id', async () => {
      jest.spyOn(model, 'findById').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCurrency),
      } as any);

      const result = await service.findOne('507f1f77bcf86cd799439011');
      expect(result).toEqual(mockCurrency);
    });

    it('should throw NotFoundException if currency not found', async () => {
      jest.spyOn(model, 'findById').mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.findOne('507f1f77bcf86cd799439011')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update a currency', async () => {
      const updateCurrencyDto: UpdateCurrencyDto = {
        sellPrice: 105,
      };

      const updatedCurrency = { ...mockCurrency, sellPrice: 105 };
      jest.spyOn(model, 'findByIdAndUpdate').mockReturnValue({
        exec: jest.fn().mockResolvedValue(updatedCurrency),
      } as any);

      const result = await service.update('507f1f77bcf86cd799439011', updateCurrencyDto);
      expect(result).toEqual(updatedCurrency);
    });

    it('should throw NotFoundException if currency not found', async () => {
      jest.spyOn(model, 'findByIdAndUpdate').mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(
        service.update('507f1f77bcf86cd799439011', {}),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should remove a currency', async () => {
      jest.spyOn(model, 'findByIdAndDelete').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCurrency),
      } as any);

      await expect(service.remove('507f1f77bcf86cd799439011')).resolves.not.toThrow();
    });

    it('should throw NotFoundException if currency not found', async () => {
      jest.spyOn(model, 'findByIdAndDelete').mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.remove('507f1f77bcf86cd799439011')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('findByName', () => {
    it('should return a currency by name', async () => {
      jest.spyOn(model, 'findOne').mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCurrency),
      } as any);

      const result = await service.findByName('USD');
      expect(result).toEqual(mockCurrency);
    });

    it('should throw NotFoundException if currency not found', async () => {
      jest.spyOn(model, 'findOne').mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.findByName('USD')).rejects.toThrow(NotFoundException);
    });
  });
});
