import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { CurrenciesService } from './currencies.service';

interface WallexApiResponse {
  success: boolean;
  result: {
    USDTTMN: {
      symbol: string;
      baseAsset: string;
      quoteAsset: string;
      faName: string;
      enName: string;
      stats: {
        '24h_ch': number;
        lastPrice: number;
        '24h_highPrice': number;
        '24h_lowPrice': number;
        '24h_volume': number;
      };
      buyStatus: string;
      sellStatus: string;
      exchangeStatus: string;
    };
  };
}

@Injectable()
export class WallexService {
  private readonly logger = new Logger(WallexService.name);
  private readonly wallexApiUrl: string;

  constructor(
    private readonly currenciesService: CurrenciesService,
    private readonly configService: ConfigService,
  ) {
    this.wallexApiUrl = this.configService.get<string>('WALLEX_API_URL') || 'https://api.wallex.ir/v1/otc/markets';
  }

  /**
   * Scheduled task to update USDT price every 10 minutes
   */
  @Cron('*/10 * * * *', {
    name: 'updateUsdtPriceWallex',
    timeZone: 'Asia/Tehran',
  })
  async updateUsdtPrice(): Promise<void> {
    this.logger.log('Starting USDT price update from Wallex...');

    try {
      const price = await this.fetchUsdtPrice();
      
      if (price) {
        await this.currenciesService.updateUsdtPrice(price.sellPrice, price.buyPrice);
        
        this.logger.log(`USDT price updated successfully from Wallex - Sell: ${price.sellPrice} Toman, Buy: ${price.buyPrice} Toman`);
      }
    } catch (error) {
      this.logger.error('Failed to update USDT price from Wallex:', error.message);
    }
  }

  /**
   * Fetch USDT price from Wallex API
   */
  private async fetchUsdtPrice(): Promise<{ sellPrice: number; buyPrice: number } | null> {
    try {
      this.logger.log('Fetching USDT price from Wallex API...');

      const response = await axios.get<WallexApiResponse>(
        this.wallexApiUrl,
        {
          timeout: 10000, // 10 seconds timeout
        }
      );

      if (!response.data.success) {
        throw new Error('Wallex API returned unsuccessful response');
      }

      const usdtData = response.data.result.USDTTMN;
      
      if (!usdtData) {
        throw new Error('USDTTMN data not found in API response');
      }

      const lastPrice = usdtData.stats.lastPrice;

      this.logger.log(`Fetched USDT price from Wallex - Last Price: ${lastPrice} Toman`);

      // For Wallex, we'll use the lastPrice for both sell and buy since it's already in Toman
      return {
        sellPrice: lastPrice,
        buyPrice: lastPrice,
      };

    } catch (error) {
      this.logger.error('Error fetching USDT price from Wallex:', error.message);
      
      if (axios.isAxiosError(error)) {
        this.logger.error(`HTTP Status: ${error.response?.status}`);
        this.logger.error(`Response Data: ${JSON.stringify(error.response?.data)}`);
      }
      
      return null;
    }
  }

  /**
   * Manual trigger for price update (for testing purposes)
   */
  async manualUpdateUsdtPrice(): Promise<void> {
    this.logger.log('Manual USDT price update from Wallex triggered');
    await this.updateUsdtPrice();
  }
} 