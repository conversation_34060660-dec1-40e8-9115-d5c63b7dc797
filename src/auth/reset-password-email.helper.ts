export function getResetPasswordEmailHtml(resetLink: string): string {
  return `
  <div style="font-family: 'Vazirmatn', <PERSON><PERSON><PERSON>, Aria<PERSON>, sans-serif; background: #f7f7f9; padding: 40px 0;">
    <table width="100%" cellpadding="0" cellspacing="0" style="max-width: 480px; margin: auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.07); overflow: hidden;">
      <tr>
        <td style="background: linear-gradient(90deg, #3b82f6 0%, #06b6d4 100%); padding: 32px 0;">
          <h2 style="margin: 0; color: #fff; font-size: 28px; font-weight: 700; text-align: center; letter-spacing: 1px;">
            بازیابی رمز عبور
          </h2>
        </td>
      </tr>
      <tr>
        <td style="padding: 32px 32px 16px 32px;">
          <p style="font-size: 18px; color: #222; margin-bottom: 16px; text-align: right;">
            کاربر گرامی،
          </p>
          <p style="font-size: 16px; color: #444; margin-bottom: 24px; text-align: right; line-height: 1.8;">
            شما درخواست بازیابی رمز عبور داده‌اید. برای تنظیم رمز عبور جدید، لطفاً روی دکمه زیر کلیک کنید:
          </p>
          <div style="text-align: center; margin-bottom: 32px;">
            <a href="${resetLink}" style="background: linear-gradient(90deg, #3b82f6 0%, #06b6d4 100%); color: #fff; text-decoration: none; padding: 14px 40px; border-radius: 8px; font-size: 18px; font-weight: 600; display: inline-block;">
              بازیابی رمز عبور
            </a>
          </div>
          <p style="font-size: 14px; color: #888; text-align: right;">
            اگر شما این درخواست را ارسال نکرده‌اید، این ایمیل را نادیده بگیرید.
          </p>
        </td>
      </tr>
      <tr>
        <td style="padding: 0 32px 32px 32px;">
          <p style="font-size: 13px; color: #bbb; text-align: center; margin-top: 32px;">
            © ${new Date().getFullYear()} تمامی حقوق محفوظ است.
          </p>
        </td>
      </tr>
    </table>
  </div>
  `;
}