import { Is<PERSON><PERSON>, <PERSON><PERSON>otEmpty, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsE<PERSON>, ValidateIf } from 'class-validator';
const mobileNumberRegex = /^(\+98|[0۰])?[9۹][0-9۰-۹]{9}$/;
export class RegisterDto {
  @IsString()
  @IsNotEmpty()
  @Matches(mobileNumberRegex)
  mobileNumber: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;
}

export class LoginDto {
  @ValidateIf((o) => !o.email)
  @IsString()
  @IsNotEmpty()
  @Matches(mobileNumberRegex)
  mobileNumber?: string;

  @ValidateIf((o) => !o.mobileNumber)
  @IsEmail()
  @IsNotEmpty()
  email?: string;

  @IsString()
  @IsNotEmpty()
  password: string;
}

export class ForgotPasswordDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;
}

export class ResetPasswordDto {
  @IsString()
  @IsNotEmpty()
  token: string;

  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;
}
