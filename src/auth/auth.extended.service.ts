import {
  Injectable,
  ConflictException,
  UnauthorizedException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Resend } from 'resend';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcryptjs';
import { User, UserDocument } from '../users/schemas/user.schema';
import {
  RegisterDto,
  LoginDto,
  ForgotPasswordDto,
  ResetPasswordDto,
} from './dto/auth.dto';
import { getResetPasswordEmailHtml } from './reset-password-email.helper';

// Utility function to convert Persian digits to English digits
function persianToEnglishDigits(input: string): string {
  const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
  const arabicDigits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  return input.replace(/[۰-۹٠-٩]/g, (d) => {
    const idx = persianDigits.indexOf(d);
    if (idx > -1) return String(idx);
    const idx2 = arabicDigits.indexOf(d);
    if (idx2 > -1) return String(idx2);
    return d;
  });
}

@Injectable()
export class AuthExtendedService {
  private resend: Resend;
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {
    this.resend = new Resend(this.configService.get<string>('RESEND_API_KEY'));
  }

  async register(
    registerDto: RegisterDto,
  ): Promise<{ accessToken: string; user: any; tokenType: string }> {
    const { mobileNumber, email, password } = registerDto;
    const normalizedMobile = persianToEnglishDigits(mobileNumber);

    // Check if mobile number already exists
    const existingMobile = await this.userModel.findOne({
      mobileNumber: normalizedMobile,
    });
    if (existingMobile)
      throw new ConflictException('شماره موبایل قبلاً ثبت شده است');

    // Check if email already exists
    const existingEmail = await this.userModel.findOne({ email });
    if (existingEmail) throw new ConflictException('ایمیل قبلاً ثبت شده است');

    const hashed = await bcrypt.hash(password, 10);
    const user = new this.userModel({
      mobileNumber: normalizedMobile,
      email,
      password: hashed,
    });
    await user.save();
    const accessToken = this.jwtService.sign({
      sub: user._id,
      mobileNumber: normalizedMobile,
      email,
    });
    // Remove password from returned user object
    const userObj = user.toObject();
    delete userObj.password;
    return { accessToken, user: userObj, tokenType: 'Bearer' };
  }

  async login(
    loginDto: LoginDto,
  ): Promise<{ accessToken: string; user: any; tokenType: string }> {
    const { mobileNumber, email, password } = loginDto;

    // Validate that at least one identifier is provided
    if (!mobileNumber && !email) {
      throw new BadRequestException('شماره موبایل یا ایمیل الزامی است');
    }

    // Build query based on provided identifier
    let query: any = {};
    let normalizedMobile: string | undefined;

    if (mobileNumber) {
      normalizedMobile = persianToEnglishDigits(mobileNumber);
      query.mobileNumber = normalizedMobile;
    }

    if (email) {
      query.email = email;
    }

    // If both are provided, use OR logic
    if (mobileNumber && email) {
      query = {
        $or: [{ mobileNumber: normalizedMobile }, { email: email }],
      };
    }

    const user = await this.userModel.findOne(query);

    if (!user) throw new NotFoundException('کاربر یافت نشد');
    if (!user.password || !(await bcrypt.compare(password, user.password))) {
      throw new UnauthorizedException('اطلاعات کاربری نامعتبر است');
    }

    const accessToken = this.jwtService.sign({
      sub: user._id,
      mobileNumber: user.mobileNumber,
      email: user.email,
    });
    const userObj = user.toObject();
    delete userObj.password;
    return { accessToken, user: userObj, tokenType: 'Bearer' };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void> {
    const { email } = forgotPasswordDto;
    const user = await this.userModel.findOne({ email });

    if (!user) {
      // To prevent user enumeration, we don't throw an error here.
      // We just silently fail.
      throw new BadRequestException('ایمیل وارد شده معتبر نیست');
    }

    const token = this.jwtService.sign({ sub: user._id }, { expiresIn: '15m' });

    const resetLink = `${this.configService.get<string>(
      'FRONTEND_URL',
    )}/auth/reset-password?token=${token}`;

    const emailResult = await this.resend.emails.send({
      from: '<EMAIL>',
      to: email,
      subject: 'بازیابی رمز عبور',
      // html: `<p>Please click the following link to reset your password: <a href="${resetLink}">${resetLink}</a></p>`,
      html: getResetPasswordEmailHtml(resetLink), // Use the new Persian HTML
    });
    console.log('Email sent:', emailResult);
    if (emailResult.error) {
      throw new BadRequestException('ارسال ایمیل بازیابی رمز عبور با خطا مواجه شد');
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    const { token, password } = resetPasswordDto;

    try {
      const payload = this.jwtService.verify(token);
      const user = await this.userModel.findById(payload.sub);

      if (!user) {
        throw new UnauthorizedException('Invalid token');
      }

      const hashedPassword = await bcrypt.hash(password, 10);
      user.password = hashedPassword;
      await user.save();
    } catch (error) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }
}
