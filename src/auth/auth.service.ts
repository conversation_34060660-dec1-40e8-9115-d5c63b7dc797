import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserDocument } from '../users/schemas/user.schema';

export interface JwtPayload {
  sub: string; // user ID
  mobileNumber: string;
  email: string;
  iat?: number;
  exp?: number;
}

@Injectable()
export class AuthService {
  constructor(private jwtService: JwtService) {}

  /**
   * Generate JWT access token for authenticated user
   */
  async generateAccessToken(user: UserDocument): Promise<string> {
    const payload: JwtPayload = {
      sub: (user._id as any).toString(),
      mobileNumber: user.mobileNumber,
      email: user.email,
    };

    return this.jwtService.sign(payload);
  }

  /**
   * Verify and decode JWT token
   */
  async verifyToken(token: string): Promise<JwtPayload> {
    return this.jwtService.verify(token);
  }
}
