import { Body, Controller, Post, HttpCode, HttpStatus } from '@nestjs/common';
import { AuthExtendedService } from './auth.extended.service';
import {
  RegisterDto,
  LoginDto,
  ForgotPasswordDto,
  ResetPasswordDto,
} from './dto/auth.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authExtendedService: AuthExtendedService) {}

  @Post('register')
  async register(@Body() registerDto: RegisterDto) {
    return this.authExtendedService.register(registerDto);
  }

  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    return this.authExtendedService.login(loginDto);
  }

  @Post('forgot-password')
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authExtendedService.forgotPassword(forgotPasswordDto);
  }

  @Post('reset-password')
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authExtendedService.resetPassword(resetPasswordDto);
  }
}
