import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BadRequestException } from '@nestjs/common';
import { UsersService } from './users.service';
import { User, UserDocument, ApprovalStatus } from './schemas/user.schema';
import { OtpService } from '../otp/otp.service';
import { AuthService } from '../auth/auth.service';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('UsersService', () => {
  let service: UsersService;
  let userModel: Model<UserDocument>;
  let otpService: OtpService;

  const mockUser = {
    _id: 'mockId',
    mobileNumber: '09123456789',
    email: '<EMAIL>',
    isActive: true,
    lastLoginAt: null,
    save: jest.fn().mockResolvedValue(this),
  };

  const mockUserModel = {
    findOne: jest.fn(),
    findById: jest.fn(),
    find: jest.fn(),
    constructor: jest.fn().mockResolvedValue(mockUser),
  } as any;

  const mockOtpService = {
    createOtp: jest.fn(),
    verifyOtp: jest.fn(),
  };

  const mockAuthService = {
    generateAccessToken: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
        {
          provide: OtpService,
          useValue: mockOtpService,
        },
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userModel = module.get<Model<UserDocument>>(getModelToken(User.name));
    otpService = module.get<OtpService>(OtpService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendOtp', () => {
    it('should send OTP to existing user', async () => {
      const sendOtpDto = { mobileNumber: '09123456789', email: '<EMAIL>' };
      const mockOtpResult = { code: '123456', expiresAt: new Date() };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockUser),
      });
      mockOtpService.createOtp.mockResolvedValue(mockOtpResult);

      const result = await service.sendOtp(sendOtpDto);

      expect(result.isNewUser).toBe(false);
      expect(result.code).toBe('123456');
      expect(mockOtpService.createOtp).toHaveBeenCalledWith('09123456789');
    });

    it('should register new user and send OTP', async () => {
      const sendOtpDto = { mobileNumber: '09123456789', email: '<EMAIL>' };
      const mockOtpResult = { code: '123456', expiresAt: new Date() };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });
      mockOtpService.createOtp.mockResolvedValue(mockOtpResult);

      // Mock the createUser method
      jest.spyOn(service, 'createUser').mockResolvedValue(mockUser as any);

      const result = await service.sendOtp(sendOtpDto);

      expect(result.isNewUser).toBe(true);
      expect(result.code).toBe('123456');
    });
  });

  describe('verifyOtp', () => {
    it('should verify OTP successfully', async () => {
      const verifyOtpDto = { mobileNumber: '09123456789', code: '123456' };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockUser),
      });
      mockOtpService.verifyOtp.mockResolvedValue(true);

      const result = await service.verifyOtp(verifyOtpDto);

      expect(result.isValid).toBe(true);
      expect(result.message).toBe('OTP verified successfully');
    });

    it('should fail OTP verification', async () => {
      const verifyOtpDto = { mobileNumber: '09123456789', code: '123456' };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockUser),
      });
      mockOtpService.verifyOtp.mockResolvedValue(false);

      const result = await service.verifyOtp(verifyOtpDto);

      expect(result.isValid).toBe(false);
      expect(result.message).toBe('Invalid or expired OTP');
    });
  });

  describe('updateUserProfile', () => {
    const mockCompleteUser = {
      _id: 'mockId',
      mobileNumber: '09123456789',
      name: 'احمد',
      familyName: 'محمدی',
      nationalCode: '1234567890',
      state: 'تهران',
      city: 'تهران',
      address: 'خیابان ولیعصر',
      nationalCardImagePath: 'uploads/user-documents/national-card.jpg',
      authImagePath: 'uploads/user-documents/auth-image.jpg',
      approvalStatus: ApprovalStatus.NOT_SUBMITTED,
      save: jest.fn().mockResolvedValue(this),
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should update profile and verify with Zibal when complete', async () => {
      const updateDto = {
        name: 'احمد',
        familyName: 'محمدی',
        nationalCode: '1234567890',
        state: 'تهران',
        city: 'تهران',
        address: 'خیابان ولیعصر',
      };

      const files = {
        nationalCardImage: [{ path: '/path/to/national-card.jpg' }] as Express.Multer.File[],
        authImage: [{ path: '/path/to/auth-image.jpg' }] as Express.Multer.File[],
      };

      // Mock successful Zibal verification
      mockedAxios.post.mockResolvedValue({
        data: {
          result: 1,
          message: 'موفق',
          data: { matched: true }
        }
      });

      mockUserModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCompleteUser),
      });

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null), // No existing user with same national code
      });

      const result = await service.updateUserProfile('mockId', updateDto, files);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://api.zibal.ir/v1/facility/shahkarInquiry',
        {
          mobile: '09123456789',
          nationalCode: '1234567890'
        },
        expect.objectContaining({
          headers: { 'Content-Type': 'application/json' },
          timeout: 10000,
        })
      );

      expect(mockCompleteUser.approvalStatus).toBe(ApprovalStatus.PENDING);
      expect(mockCompleteUser.save).toHaveBeenCalled();
    });

    it('should throw error when Zibal verification fails', async () => {
      const updateDto = {
        name: 'احمد',
        familyName: 'محمدی',
        nationalCode: '1234567890',
        state: 'تهران',
        city: 'تهران',
        address: 'خیابان ولیعصر',
      };

      const files = {
        nationalCardImage: [{ path: '/path/to/national-card.jpg' }] as Express.Multer.File[],
        authImage: [{ path: '/path/to/auth-image.jpg' }] as Express.Multer.File[],
      };

      // Mock failed Zibal verification
      mockedAxios.post.mockResolvedValue({
        data: {
          result: 0,
          message: 'عدم تطابق',
          data: { matched: false }
        }
      });

      mockUserModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCompleteUser),
      });

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.updateUserProfile('mockId', updateDto, files))
        .rejects.toThrow(BadRequestException);
      await expect(service.updateUserProfile('mockId', updateDto, files))
        .rejects.toThrow('National code and mobile number do not match');
    });

    it('should throw error when Zibal API fails', async () => {
      const updateDto = {
        name: 'احمد',
        familyName: 'محمدی',
        nationalCode: '1234567890',
        state: 'تهران',
        city: 'تهران',
        address: 'خیابان ولیعصر',
      };

      const files = {
        nationalCardImage: [{ path: '/path/to/national-card.jpg' }] as Express.Multer.File[],
        authImage: [{ path: '/path/to/auth-image.jpg' }] as Express.Multer.File[],
      };

      // Mock Zibal API error
      mockedAxios.post.mockRejectedValue(new Error('Network error'));

      mockUserModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCompleteUser),
      });

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.updateUserProfile('mockId', updateDto, files))
        .rejects.toThrow(BadRequestException);
      await expect(service.updateUserProfile('mockId', updateDto, files))
        .rejects.toThrow('Unable to verify national code and mobile number');
    });

    it('should not call Zibal API when profile is incomplete', async () => {
      const updateDto = {
        name: 'احمد',
      };

      const incompleteUser = {
        _id: 'mockId',
        mobileNumber: '09123456789',
        name: 'احمد',
        familyName: undefined,
        nationalCode: undefined,
        state: undefined,
        city: undefined,
        address: undefined,
        nationalCardImagePath: undefined,
        authImagePath: undefined,
        approvalStatus: ApprovalStatus.NOT_SUBMITTED,
        save: jest.fn().mockResolvedValue(this),
      };

      mockUserModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(incompleteUser),
      });

      await service.updateUserProfile('mockId', updateDto);

      expect(mockedAxios.post).not.toHaveBeenCalled();
      expect(incompleteUser.approvalStatus).toBe(ApprovalStatus.NOT_SUBMITTED);
    });
  });
});
