import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserDocument = User & Document;

export enum ApprovalStatus {
  NOT_SUBMITTED = 'not_submitted',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

@Schema({
  timestamps: true,
})
export class User {
  @Prop()
  password?: string;
  @Prop({
    required: true,
    unique: true,
    match: /^(\+98|0)?9\d{9}$/ // Iranian mobile number format
  })
  mobileNumber: string;

  @Prop({
    required: true,
    unique: true,
    match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ // Email format validation
  })
  email: string;

  @Prop({ default: true })
  isActive: boolean;

  @Prop()
  lastLoginAt?: Date;

  // Profile information fields
  @Prop()
  name?: string;

  @Prop()
  familyName?: string;

  @Prop({
    unique: true,
    sparse: true, // Allows multiple null values but unique non-null values
    match: /^\d{10}$/ // Iranian national code format (10 digits)
  })
  nationalCode?: string;

  // Address information fields
  @Prop()
  state?: string;

  @Prop()
  city?: string;

  @Prop()
  address?: string;

  // Document file paths
  @Prop()
  nationalCardImagePath?: string;

  @Prop()
  authImagePath?: string;

  // Approval status and submission tracking
  @Prop({
    type: String,
    enum: ApprovalStatus,
    default: ApprovalStatus.NOT_SUBMITTED
  })
  approvalStatus: ApprovalStatus;

  @Prop()
  submittedAt?: Date;
}

export const UserSchema = SchemaFactory.createForClass(User);
