import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { CurrenciesModule } from './currencies/currencies.module';
import { UsersModule } from './users/users.module';
import { OtpModule } from './otp/otp.module';
import { AuthModule } from './auth/auth.module';
import { BankCardsModule } from './bank-cards/bank-cards.module';
import { OrdersModule } from './orders/orders.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    AuthModule,
    CurrenciesModule,
    UsersModule,
    OtpModule,
    BankCardsModule,
    OrdersModule,
    ConfigModule.forRoot({
      envFilePath: `.env.${process.env.NODE_ENV}`,
      isGlobal: true,
    }),
    // MongooseModule.forRootAsync({
    //   imports: [ConfigModule],
    //   useFactory: async (configService: ConfigService) => {
    //     const baseConfig = {
    //       uri: configService.get<string>('DB_URI'),
    //       dbName: configService.get<string>('DB_NAME'),
    //       auth: {
    //         username: configService.get<string>('DB_USERNAME'),
    //         password: configService.get<string>('DB_PASSWORD'),
    //       },
    //     };
    //     return baseConfig;
    //   },
    //   inject: [ConfigService],
    // }),
    MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost:27017/sarafi', {
      authSource: 'admin', // Optional, specify if authentication is required
    }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
