# Testing Zibal Card Verification Integration

## Overview
This document provides examples of how to test the new Zibal API integration for card number and cardholder name verification.

## Prerequisites
1. Start the development server: `npm run start:dev`
2. Have a valid JWT token from user authentication
3. Ensure MongoDB is running

## Test Scenarios

### Scenario 1: Get JWT Token First

**Step 1: Send OTP**
```bash
curl -X POST http://localhost:3000/users/send-otp \
  -H "Content-Type: application/json" \
  -d '{
    "mobileNumber": "***********"
  }'
```

**Step 2: Verify OTP (use the code from the response above)**
```bash
curl -X POST http://localhost:3000/users/verify-otp \
  -H "Content-Type: application/json" \
  -d '{
    "mobileNumber": "***********",
    "code": "123456"
  }'
```

Save the JWT token from the response for the next steps.

### Scenario 2: Create Bank Card with Verification

**Test with Valid Card Data:**
```bash
curl -X POST http://localhost:3000/cards \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "cardNumber": "****************",
    "bankName": "بانک ملت",
    "accountHolderName": "احمد محمدی",
    "iban": "**************************",
    "description": "کارت اصلی"
  }'
```

**Expected Behavior:**
- The system will call Zibal API to verify the card number and cardholder name
- If verification succeeds, the card will be created successfully
- If verification fails, you'll get a 400 error with Persian message

### Scenario 3: Update Bank Card with Verification

**Update Account Holder Name:**
```bash
curl -X PATCH http://localhost:3000/cards/CARD_ID_HERE \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "accountHolderName": "احمد محمدی جدید"
  }'
```

**Update Card Number:**
```bash
curl -X PATCH http://localhost:3000/cards/CARD_ID_HERE \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "cardNumber": "****************"
  }'
```

**Expected Behavior:**
- Verification will only occur if card number or account holder name is being changed
- Other field updates (bankName, iban, description, isActive) won't trigger verification

## Expected Responses

### Success Response (Card Created/Updated)
```json
{
  "success": true,
  "message": "Bank card created successfully",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "userId": "507f1f77bcf86cd799439012",
    "cardNumber": "**** **** **** 7890",
    "bankName": "بانک ملت",
    "accountHolderName": "احمد محمدی",
    "iban": "**************************",
    "isActive": true,
    "description": "کارت اصلی",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Verification Failed Response
```json
{
  "success": false,
  "message": "شماره کارت و نام صاحب کارت مطابقت ندارند. لطفاً اطلاعات خود را بررسی کنید.",
  "statusCode": 400,
  "error": "Bad Request"
}
```

### API Error Response
```json
{
  "success": false,
  "message": "امکان تأیید تطابق شماره کارت و نام صاحب کارت وجود ندارد. لطفاً دوباره تلاش کنید.",
  "statusCode": 400,
  "error": "Bad Request"
}
```

### Duplicate Card Response
```json
{
  "success": false,
  "message": "این شماره کارت قبلاً برای حساب شما ثبت شده است",
  "statusCode": 400,
  "error": "Bad Request"
}
```

## Logs to Monitor

When testing, monitor the application logs for:

**Successful Verification:**
```
[BankCardsService] Verifying card **** **** **** 7890 with name "احمد محمدی" via Zibal API
[BankCardsService] Card and name verification successful for **** **** **** 7890
```

**Failed Verification:**
```
[BankCardsService] Verifying card **** **** **** 7890 with name "احمد محمدی" via Zibal API
[BankCardsService] Card and name verification failed for **** **** **** 7890: عدم تطابق
```

**API Error:**
```
[BankCardsService] Verifying card **** **** **** 7890 with name "احمد محمدی" via Zibal API
[BankCardsService] Error verifying card and name via Zibal API: Network error
```

## Testing Different Scenarios

### Test Case 1: Valid Card and Name Match
- Use a real Iranian bank card number and the correct cardholder name
- Should result in successful card creation

### Test Case 2: Valid Card but Wrong Name
- Use a real Iranian bank card number but incorrect cardholder name
- Should result in verification failure

### Test Case 3: Invalid Card Number
- Use an invalid or non-existent card number
- Should result in API error or verification failure

### Test Case 4: Network Issues
- Temporarily disconnect internet or block Zibal API
- Should result in API error message

## Notes

1. **Real vs Test Data**: The Zibal API works with real Iranian bank card data. For testing, you may need to use actual card numbers and names.

2. **Rate Limiting**: Be mindful of API rate limits when testing multiple requests.

3. **Error Handling**: The system is designed to fail securely - if verification cannot be completed, card creation/update is prevented.

4. **Logging**: All verification attempts are logged with masked card numbers for security.

5. **Timeout**: API calls have a 10-second timeout to prevent hanging requests.

## Troubleshooting

- **Port Already in Use**: Kill existing processes with `lsof -ti:3000 | xargs kill -9`
- **MongoDB Connection**: Ensure MongoDB is running and accessible
- **JWT Token Expired**: Get a new token using the OTP flow
- **Invalid Card Format**: Ensure card number is exactly 16 digits
- **IBAN Format**: Ensure IBAN follows Iranian format (IR + 24 digits)
