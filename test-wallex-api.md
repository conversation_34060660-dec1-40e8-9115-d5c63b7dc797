# Wallex API Integration Test

## Overview
This document describes how to test the new Wallex service for USDT price updates.

## API Endpoint
- **URL**: `https://api.wallex.ir/v1/otc/markets`
- **Method**: GET
- **Response**: JSON with USDTTMN market data

## Manual Test Endpoint
- **URL**: `POST /currencies/update-usdt-price-wallex`
- **Description**: Manually trigger USDT price update from Wallex

## Expected Response Structure
```json
{
  "success": true,
  "result": {
    "USDTTMN": {
      "symbol": "USDTTMN",
      "baseAsset": "USDT",
      "quoteAsset": "TMN",
      "stats": {
        "lastPrice": 84573,
        "24h_highPrice": 86000,
        "24h_lowPrice": 84000
      }
    }
  }
}
```

## Testing Steps

1. **Start the application**:
   ```bash
   npm run start:dev
   ```

2. **Test the manual endpoint**:
   ```bash
   curl -X POST http://localhost:3000/currencies/update-usdt-price-wallex
   ```

3. **Expected response**:
   ```json
   {
     "success": true,
     "message": "USDT price update from Wallex triggered successfully"
   }
   ```

4. **Verify price update**:
   ```bash
   curl http://localhost:3000/currencies/name/USDT
   ```

## Configuration
The service uses the following environment variable (optional):
- `WALLEX_API_URL`: Defaults to `https://api.wallex.ir/v1/otc/markets`

## Notes
- The service extracts the `lastPrice` from the USDTTMN market data
- Prices are already in Toman (no conversion needed)
- The service uses the same price for both sell and buy
- Error handling includes detailed logging for debugging 