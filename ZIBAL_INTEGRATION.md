# Zibal API Integration for National Code and Mobile Number Verification

## Overview

The application now integrates with Zibal's Shahkar Inquiry API to verify that a user's national code (کد ملی) and mobile number match according to official records. This verification is performed automatically when a user completes their profile.

## API Endpoint Used

**Zibal <PERSON> Inquiry API**
- URL: `https://api.zibal.ir/v1/facility/shahkarInquiry`
- Method: `POST`
- Content-Type: `application/json`

### Request Format
```json
{
    "mobile": "092XXX7725",
    "nationalCode": "1285XXX561"
}
```

### Response Format
```json
{
    "message": "موفق",
    "data": {
        "matched": true
    },
    "result": 1
}
```

## Integration Details

### When Verification Occurs

The Zibal verification is automatically triggered when:
1. A user updates their profile via the `PATCH /users/profile` endpoint
2. All required profile fields are complete:
   - `name`
   - `familyName`
   - `nationalCode`
   - `state`
   - `city`
   - `address`
   - `nationalCardImage` (file upload)
   - `authImage` (file upload)

### Verification Process

1. **Profile Completion Check**: The system first verifies that all required fields are present
2. **Zibal API Call**: If the profile is complete, the system calls Zibal's API with the user's national code and mobile number
3. **Verification Result**:
   - **Success**: If `result === 1` and `data.matched === true`, the profile is approved for submission
   - **Failure**: If verification fails, a `BadRequestException` is thrown with an appropriate error message
   - **API Error**: If the Zibal API is unavailable, a `BadRequestException` is thrown to prevent profile completion

### Code Implementation

The verification logic is implemented in the `UsersService` class:

```typescript
private async verifyNationalCodeAndMobile(nationalCode: string, mobileNumber: string): Promise<boolean> {
  try {
    const response = await axios.post(
      'https://api.zibal.ir/v1/facility/shahkarInquiry',
      {
        mobile: mobileNumber,
        nationalCode: nationalCode
      },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000,
      }
    );

    return response.data.result === 1 && response.data.data?.matched === true;
  } catch (error) {
    // Log error and throw exception to prevent profile completion
    throw new BadRequestException('Unable to verify national code and mobile number. Please try again later.');
  }
}
```

## Error Handling

### User-Facing Errors

1. **Verification Mismatch**:
   - Message: "National code and mobile number do not match. Please verify your information."
   - HTTP Status: 400 (Bad Request)

2. **API Unavailable**:
   - Message: "Unable to verify national code and mobile number. Please try again later."
   - HTTP Status: 400 (Bad Request)

### Logging

The system logs all verification attempts and errors:
- Successful verifications are logged at INFO level
- Failed verifications are logged at WARN level
- API errors are logged at ERROR level with full error details

## Testing

### Unit Tests

The integration includes comprehensive unit tests covering:
- Successful verification scenarios
- Failed verification scenarios
- API error scenarios
- Profile completion logic

### Test Coverage

- ✅ Profile completion with successful Zibal verification
- ✅ Profile completion with failed Zibal verification
- ✅ API error handling
- ✅ Incomplete profile scenarios (no API call made)

## Security Considerations

1. **Timeout**: API calls have a 10-second timeout to prevent hanging requests
2. **Error Handling**: API failures prevent profile completion to maintain data integrity
3. **Logging**: All verification attempts are logged for audit purposes
4. **Data Privacy**: Only national code and mobile number are sent to Zibal API

## Usage Example

When a user completes their profile:

```bash
curl -X PATCH http://localhost:3000/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: multipart/form-data" \
  -F "name=احمد" \
  -F "familyName=محمدی" \
  -F "nationalCode=1234567890" \
  -F "state=تهران" \
  -F "city=تهران" \
  -F "address=خیابان ولیعصر، پلاک ۱۲۳" \
  -F "nationalCardImage=@national-card.jpg" \
  -F "authImage=@auth-image.jpg"
```

**Success Response** (if verification passes):
```json
{
  "success": true,
  "message": "Profile completed and submitted for approval",
  "data": {
    "approvalStatus": "pending",
    "submittedAt": "2025-08-04T19:57:34.000Z",
    // ... other user data
  }
}
```

**Error Response** (if verification fails):
```json
{
  "success": false,
  "message": "National code and mobile number do not match. Please verify your information.",
  "statusCode": 400
}
```

## Future Enhancements

1. **Retry Logic**: Implement automatic retry for transient API failures
2. **Caching**: Cache successful verifications to avoid repeated API calls
3. **Configuration**: Make API endpoint configurable via environment variables
4. **Rate Limiting**: Implement rate limiting for API calls to prevent abuse
