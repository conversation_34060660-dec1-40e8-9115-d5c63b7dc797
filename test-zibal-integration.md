# Testing Zibal Integration

## Overview
This document provides examples of how to test the new Zibal API integration for national code and mobile number verification.

## Prerequisites
1. Start the development server: `npm run start:dev`
2. Have a valid JWT token from user authentication
3. Ensure MongoDB is running

## Test Scenarios

### Scenario 1: Complete Profile with Valid Verification

**Step 1: Send OTP and get JWT token**
```bash
# Send OTP
curl -X POST http://localhost:3000/users/send-otp \
  -H "Content-Type: application/json" \
  -d '{
    "mobileNumber": "09123456789",
    "email": "<EMAIL>"
  }'

# Verify OTP (use the code from the response above)
curl -X POST http://localhost:3000/users/verify-otp \
  -H "Content-Type: application/json" \
  -d '{
    "mobileNumber": "09123456789",
    "code": "123456"
  }'
```

**Step 2: Update profile with complete data**
```bash
curl -X PATCH http://localhost:3000/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: multipart/form-data" \
  -F "name=احمد" \
  -F "familyName=محمدی" \
  -F "nationalCode=1234567890" \
  -F "state=تهران" \
  -F "city=تهران" \
  -F "address=خیابان ولیعصر، پلاک ۱۲۳" \
  -F "nationalCardImage=@/path/to/national-card.jpg" \
  -F "authImage=@/path/to/auth-image.jpg"
```

**Expected Behavior:**
- The system will call Zibal API to verify the national code and mobile number
- If verification succeeds, the profile status will be set to "pending"
- If verification fails, you'll get a 400 error with message about mismatch

### Scenario 2: Incomplete Profile (No Zibal Call)

```bash
curl -X PATCH http://localhost:3000/users/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "احمد"
  }'
```

**Expected Behavior:**
- No Zibal API call will be made
- Profile status remains "not_submitted"
- Response indicates profile is incomplete

### Scenario 3: Testing Error Handling

To test error handling, you can temporarily modify the Zibal API URL in the code to an invalid endpoint, or use network tools to simulate API failures.

## Response Examples

### Successful Profile Completion
```json
{
  "success": true,
  "message": "Profile completed and submitted for approval",
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "mobileNumber": "09123456789",
    "name": "احمد",
    "familyName": "محمدی",
    "nationalCode": "1234567890",
    "state": "تهران",
    "city": "تهران",
    "address": "خیابان ولیعصر، پلاک ۱۲۳",
    "approvalStatus": "pending",
    "submittedAt": "2025-08-04T19:57:34.000Z",
    "hasNationalCardImage": true,
    "hasAuthImage": true,
    "isProfileComplete": true,
    "createdAt": "2025-08-04T19:50:00.000Z",
    "updatedAt": "2025-08-04T19:57:34.000Z"
  }
}
```

### Verification Failure
```json
{
  "success": false,
  "message": "National code and mobile number do not match. Please verify your information.",
  "statusCode": 400,
  "error": "Bad Request"
}
```

### API Error
```json
{
  "success": false,
  "message": "Unable to verify national code and mobile number. Please try again later.",
  "statusCode": 400,
  "error": "Bad Request"
}
```

## Logs to Monitor

When testing, monitor the application logs for:

```
[UsersService] Verifying national code 1234567890 with mobile 09123456789 via Zibal API
[UsersService] National code and mobile verification successful for 1234567890
```

Or in case of errors:
```
[UsersService] National code and mobile verification failed for 1234567890: عدم تطابق
[UsersService] Error verifying national code and mobile via Zibal API: Network error
```

## Notes

1. **Development Environment**: In development, the actual Zibal API calls will be made, so ensure you have internet connectivity
2. **Test Data**: Use test national codes and mobile numbers that you know the verification status for
3. **Rate Limiting**: Be mindful of potential rate limiting on the Zibal API
4. **Error Simulation**: To test error scenarios, you can temporarily modify the API URL or use network simulation tools

## Integration Points

The Zibal verification is integrated at the following point in the user flow:
1. User completes all required profile fields
2. System checks if profile is complete using `isProfileComplete()`
3. If complete, system calls `verifyNationalCodeAndMobile()`
4. Based on verification result, profile status is set to "pending" or error is thrown
5. User receives appropriate response indicating success or failure
